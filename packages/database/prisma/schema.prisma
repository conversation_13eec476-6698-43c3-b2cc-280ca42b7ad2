// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/client"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

//// User model (core entity for all roles)

model User {
  id            String    @id @default(uuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @map("email_verified")
  image         String?
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  sessions      Session[]
  accounts      Account[]
  role          String    @default("customer") // super-admin, admin, customer
  banned        Boolean?  @default(false)
  banReason     String?   @map("ban_reason")
  banExpires    DateTime? @map("ban_expires")

  firstName String?   @map("first_name")
  lastName  String?   @map("last_name")
  phone     String?
  dob       DateTime?

  @@map("user")
}

//// Auth models

model Session {
  id             String   @id @default(uuid())
  expiresAt      DateTime @map("expires_at")
  token          String   @unique
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  ipAddress      String?  @map("ip_address")
  userAgent      String?  @map("user_agent")
  userId         String   @map("user_id")
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  impersonatedBy String?  @map("impersonated_by")

  @@map("session")
}

model Account {
  id                    String    @id @default(uuid())
  accountId             String?   @map("account_id") // oauth account id
  providerId            String    @map("provider_id") // account type: credential, google, apple, oauth...
  userId                String    @map("user_id")
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?   @map("access_token")
  refreshToken          String?   @map("refresh_token")
  idToken               String?   @map("id_token")
  accessTokenExpiresAt  DateTime? @map("access_token_expires_at")
  refreshTokenExpiresAt DateTime? @map("refresh_token_expires_at")
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  @@map("account")
}

model Verification {
  id         String    @id @default(uuid())
  identifier String
  value      String
  expiresAt  DateTime  @map("expires_at")
  createdAt  DateTime? @default(now()) @map("created_at")
  updatedAt  DateTime? @updatedAt @map("updated_at")

  @@map("verification")
}

model Jwks {
  id         String   @id
  publicKey  String
  privateKey String
  createdAt  DateTime

  @@map("jwks")
}

//// System

// CMS Content model for dynamic content
model CmsContent {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Content key and type
  key         String  @unique // e.g., "delivery_note", "how_to_exchange", "how_to_return", "about_return", "about_refund"
  title       String // Display title for the admin interface
  description String? @db.Text // Optional description for admin interface

  // Content by locale
  content     Json // JSON object with locale keys (e.g., {"en": "content", "ja": "コンテンツ"})
  isPublished Boolean @default(true) @map("is_published")

  @@index([key])
  @@map("cms_content")
}

// Return/Exchange Request model
model ReturnRequest {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Order information
  orderName    String @map("order_name")
  email        String
  returnNumber String @unique @map("return_number")

  // Return details
  returnReason  String  @map("return_reason") // wrong_size_model, defect, different_description, etc.
  exchangeType  String? @map("exchange_type") // exchange or return
  defectType    String? @map("defect_type") // broken, scratched, dirt, other
  defectDetails String? @map("defect_details") @db.Text

  // Return options
  returnLabelOption String? @map("return_label_option") // print or envelope
  refundFee         Float?  @map("refund_fee")

  // Status
  status    String  @default("pending") // pending, approved, rejected, completed
  processed Boolean @default(false)

  // Relationships
  returnItems  ReturnItem[]
  defectPhotos DefectPhoto[]

  // Tracking
  adminNotes String? @map("admin_notes") @db.Text

  @@index([orderName, email])
  @@index([returnNumber])
  @@map("return_requests")
}

// Individual items being returned
model ReturnItem {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Item details
  lineItemId   String  @map("line_item_id")
  title        String
  variantTitle String? @map("variant_title")
  quantity     Int
  price        Float
  currency     String
  sku          String?

  // Return details
  returnReason    String? @map("return_reason")
  exchangeVariant String? @map("exchange_variant") // For exchanges, the new variant ID

  // Relationships
  returnRequest   ReturnRequest @relation(fields: [returnRequestId], references: [id], onDelete: Cascade)
  returnRequestId String

  @@index([returnRequestId])
  @@index([lineItemId])
  @@map("return_items")
}

// Photos for defect documentation
model DefectPhoto {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")

  // Photo details
  url String

  // Relationships
  returnRequest   ReturnRequest @relation(fields: [returnRequestId], references: [id], onDelete: Cascade)
  returnRequestId String

  @@index([returnRequestId])
  @@map("defect_photos")
}

// Address Change Requests
model AddressChangeRequest {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Order information
  orderName String @map("order_name")
  email     String

  // Original address
  originalAddress Json @map("original_address")

  // New address
  newAddress Json @map("new_address")

  // Status
  status String @default("pending") // pending, approved, rejected, completed

  // Tracking
  adminNotes String? @map("admin_notes") @db.Text

  @@index([orderName, email])
  @@map("address_change_requests")
}

// Receipt Customization
model ReceiptCustomization {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Order information
  orderName String @map("order_name")
  email     String

  // Customization
  recipientName String? @map("recipient_name")
  note          String? @map("note")

  // Edit lock mechanism
  isCustomized Boolean @default(false) @map("is_customized")

  // Download tracking
  downloadCount  Int       @default(0) @map("download_count")
  lastDownloaded DateTime? @map("last_downloaded")

  @@unique([orderName, email])
  @@map("receipt_customizations")
}

// Feature Flags and Settings
model FeatureSettings {
  id          String  @id @default(cuid())
  name        String  @unique
  enabled     Boolean @default(true)
  value       String?
  description String?

  // For region-specific settings
  region String? // JP, GLOBAL, etc.

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("feature_settings")
}

// Admin Notifications
model AdminNotification {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")

  // Notification details
  type             String // return_request, address_change, etc.
  message          String
  relatedOrderName String? @map("related_order_name")

  // Status
  read   Boolean   @default(false)
  readAt DateTime? @map("read_at")

  @@index([type])
  @@index([read])
  @@map("admin_notifications")
}

// Blacklisted Emails
model BlacklistEmail {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  email  String @unique
  reason String // 'excessive_returns', 'manually_added', etc.

  @@map("blacklist_emails")
}

// App Settings
model AppSettings {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  key         String  @unique
  value       String
  description String?

  @@map("app_settings")
}
