'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Camera, CheckCircle, Package, XCircle } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface BarcodeScannerProps {
  returnItems: Array<{
    id: string;
    title: string;
    sku: string | null;
    quantity: number;
    scanned?: boolean;
  }>;
  onItemScanned: (itemId: string, scannedSku: string) => void;
}

export function BarcodeScanner({
  returnItems,
  onItemScanned,
}: BarcodeScannerProps) {
  const [isScanning, setIsScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState<string>('');
  const [scanResult, setScanResult] = useState<{
    success: boolean;
    message: string;
    itemId?: string;
  } | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const startScanning = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }, // Use back camera
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsScanning(true);
        setScanResult(null);
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setScanResult({
        success: false,
        message: 'Unable to access camera. Please check permissions.',
      });
    }
  };

  const stopScanning = () => {
    if (streamRef.current) {
      for (const track of streamRef.current.getTracks()) {
        track.stop();
      }
      streamRef.current = null;
    }
    setIsScanning(false);
  };

  const handleManualEntry = () => {
    if (!scannedCode.trim()) {
      return;
    }

    processScannedCode(scannedCode.trim());
    setScannedCode('');
  };

  const processScannedCode = (code: string) => {
    // Find matching item by SKU
    const matchingItem = returnItems.find(
      (item) => item.sku && item.sku.toLowerCase() === code.toLowerCase()
    );

    if (matchingItem) {
      onItemScanned(matchingItem.id, code);
      setScanResult({
        success: true,
        message: `Successfully scanned: ${matchingItem.title}`,
        itemId: matchingItem.id,
      });
    } else {
      setScanResult({
        success: false,
        message: `No matching item found for SKU: ${code}`,
      });
    }

    stopScanning();
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, []);

  const scannedCount = returnItems.filter((item) => item.scanned).length;
  const totalCount = returnItems.length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Item Verification
        </CardTitle>
        <CardDescription>
          Scan barcodes to verify return items ({scannedCount}/{totalCount}{' '}
          verified)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress indicator */}
        <div className="h-2 w-full rounded-full bg-gray-200">
          <div
            className="h-2 rounded-full bg-blue-600 transition-all duration-300"
            style={{ width: `${(scannedCount / totalCount) * 100}%` }}
          />
        </div>

        {/* Camera view */}
        {isScanning && (
          <div className="relative">
            {/* biome-ignore lint/a11y/useMediaCaption: <explanation> */}
            <video
              ref={videoRef}
              autoPlay
              playsInline
              className="h-64 w-full rounded-lg border object-cover"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="h-32 w-48 rounded-lg border-2 border-white border-dashed" />
            </div>
          </div>
        )}

        {/* Manual entry */}
        <div className="space-y-2">
          <label htmlFor="manual-entry" className="font-medium text-sm">
            Manual SKU Entry
          </label>
          <div className="flex gap-2">
            <input
              aria-labelledby="manual-entry"
              type="text"
              value={scannedCode}
              onChange={(e) => setScannedCode(e.target.value)}
              placeholder="Enter SKU manually"
              className="flex-1 rounded-md border border-gray-300 px-3 py-2"
              onKeyPress={(e) => e.key === 'Enter' && handleManualEntry()}
            />
            <Button
              onClick={handleManualEntry}
              disabled={!scannedCode.trim()}
              variant="outline"
            >
              Add
            </Button>
          </div>
        </div>

        {/* Scan result */}
        {scanResult && (
          <div
            className={`flex items-center gap-2 rounded-lg p-3 ${
              scanResult.success
                ? 'border border-green-200 bg-green-50 text-green-800'
                : 'border border-red-200 bg-red-50 text-red-800'
            }`}
          >
            {scanResult.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <span className="text-sm">{scanResult.message}</span>
          </div>
        )}

        {/* Camera controls */}
        <div className="flex gap-2">
          {isScanning ? (
            <Button onClick={stopScanning} variant="outline">
              Stop Camera
            </Button>
          ) : (
            <Button onClick={startScanning} className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              Start Camera
            </Button>
          )}
        </div>

        {/* Items list */}
        <div className="space-y-2">
          <h4 className="font-medium">Return Items</h4>
          {returnItems.map((item) => (
            <div
              key={item.id}
              className={`rounded-lg border p-3 ${
                item.scanned
                  ? 'border-green-200 bg-green-50'
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{item.title}</p>
                  {item.sku && (
                    <p className="text-gray-600 text-sm">SKU: {item.sku}</p>
                  )}
                  <p className="text-gray-600 text-sm">Qty: {item.quantity}</p>
                </div>
                <Badge variant={item.scanned ? 'default' : 'secondary'}>
                  {item.scanned ? 'Verified' : 'Pending'}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
