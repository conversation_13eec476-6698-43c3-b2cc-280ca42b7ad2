'use client';

import { log } from '@repo/observability/log';
import { BarcodeScanner } from './barcode-scanner';

interface BarcodeScannerTabProps {
  returnRequest: {
    id: string;
    returnItems: Array<{
      id: string;
      title: string;
      sku: string | null;
      quantity: number;
    }>;
  };
}

export function BarcodeScannerTab({ returnRequest }: BarcodeScannerTabProps) {
  const handleItemScanned = (
    itemId: string,
    scannedSku: string,
    quantity: number
  ) => {
    // Here you could also save the scanned status to the database
    log.info(
      `Item ${itemId} scanned with SKU: ${scannedSku}, quantity: ${quantity}`
    );
  };

  const handleSubmit = (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => {
    // Handle form submission - could save to database or trigger next step
    log.info('All items verified:', scannedItems);
    // You could add API call here to save the verification status
  };

  return (
    <BarcodeScanner
      returnItems={returnRequest.returnItems}
      onItemScanned={handleItemScanned}
      onSubmit={handleSubmit}
    />
  );
}
