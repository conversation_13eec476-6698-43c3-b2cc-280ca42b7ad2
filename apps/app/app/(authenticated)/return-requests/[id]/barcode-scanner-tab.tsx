'use client';

import { log } from '@repo/observability/log';
import { useState } from 'react';
import { BarcodeScanner } from './barcode-scanner';

interface BarcodeScannerTabProps {
  returnRequest: {
    id: string;
    returnItems: Array<{
      id: string;
      title: string;
      sku: string | null;
      quantity: number;
    }>;
  };
}

export function BarcodeScannerTab({ returnRequest }: BarcodeScannerTabProps) {
  const [scannedItems, setScannedItems] = useState<Record<string, boolean>>({});

  const handleItemScanned = (itemId: string, scannedSku: string) => {
    setScannedItems((prev) => ({
      ...prev,
      [itemId]: true,
    }));

    // Here you could also save the scanned status to the database
    log.info(`Item ${itemId} scanned with SKU: ${scannedSku}`);
  };

  const returnItemsWithScannedStatus = returnRequest.returnItems.map(
    (item) => ({
      ...item,
      scanned: scannedItems[item.id] || false,
    })
  );

  return (
    <BarcodeScanner
      returnItems={returnItemsWithScannedStatus}
      onItemScanned={handleItemScanned}
    />
  );
}
