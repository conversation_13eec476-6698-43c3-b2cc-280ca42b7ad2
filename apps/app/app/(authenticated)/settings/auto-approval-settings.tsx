'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import { getAutoApprovalDays, updateAutoApprovalDays } from './actions';

// Form validation schema
const autoApprovalSchema = z.object({
  days: z
    .number()
    .min(1, 'Days must be at least 1')
    .max(365, 'Days cannot exceed 365'),
});

type AutoApprovalFormValues = z.infer<typeof autoApprovalSchema>;

export function AutoApprovalSettings() {
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Initialize form with react-hook-form
  const form = useForm<AutoApprovalFormValues>({
    resolver: zodResolver(autoApprovalSchema),
    defaultValues: {
      days: 30,
    },
  });

  useEffect(() => {
    loadAutoApprovalDays();
  }, []);

  const loadAutoApprovalDays = async () => {
    try {
      const currentDays = await getAutoApprovalDays();
      form.setValue('days', currentDays);
    } catch (error) {
      console.error('Failed to load auto-approval days:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (values: AutoApprovalFormValues) => {
    try {
      await updateAutoApprovalDays(values.days);
      form.reset(values); // Reset form to current values to clear dirty state
      toast({
        title: 'Success',
        description: 'Auto-approval settings saved successfully',
      });
    } catch (error) {
      console.error('Failed to save auto-approval days:', error);
      toast({
        title: 'Error',
        description: 'Failed to save auto-approval settings',
        variant: 'destructive',
      });
    }
  };

  const handleReset = () => {
    form.reset();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Auto-Approval Settings</CardTitle>
        <CardDescription>
          Configure the number of days within which return requests are
          automatically approved. Requests outside this window will require
          manual review.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        ) : (
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSave)}
              className="space-y-4"
            >
              <FormField
                control={form.control}
                name="days"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Number of days allowed for auto-approval
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="365"
                        className="w-32"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormDescription>
                      Return requests made within {form.watch('days')} days of
                      purchase will be automatically approved. Valid range:
                      1-365 days.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {form.formState.isDirty && (
                <div className="flex gap-2 border-t pt-4">
                  <Button
                    type="submit"
                    disabled={form.formState.isSubmitting}
                    className="flex items-center gap-2"
                  >
                    {form.formState.isSubmitting ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    ) : null}
                    Save Changes
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleReset}
                    disabled={form.formState.isSubmitting}
                  >
                    Reset
                  </Button>
                </div>
              )}
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
