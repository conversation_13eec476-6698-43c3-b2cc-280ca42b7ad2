'use server';

import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';

// Blacklist email management
export async function getBlacklistedEmails(): Promise<string[]> {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  const blacklistedEmails = await database.blacklistEmail.findMany({
    select: {
      email: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return blacklistedEmails.map((item) => item.email);
}

export async function addEmailToBlacklist(email: string): Promise<void> {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  // Check if email already exists
  const existing = await database.blacklistEmail.findUnique({
    where: { email },
  });

  if (existing) {
    throw new Error('Email is already blacklisted');
  }

  await database.blacklistEmail.create({
    data: {
      email,
      reason: 'manually_added',
    },
  });

  revalidatePath('/settings');
}

export async function removeEmailFromBlacklist(email: string): Promise<void> {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  await database.blacklistEmail.delete({
    where: { email },
  });

  revalidatePath('/settings');
}

// Auto-approval settings management
export async function getAutoApprovalDays(): Promise<number> {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  const settings = await database.appSettings.findFirst({
    where: {
      key: 'auto_approval_days',
    },
  });

  return settings ? Number.parseInt(settings.value, 10) : 30; // Default to 30 days
}

export async function updateAutoApprovalDays(days: number): Promise<void> {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  if (days < 1 || days > 365) {
    throw new Error('Days must be between 1 and 365');
  }

  await database.appSettings.upsert({
    where: {
      key: 'auto_approval_days',
    },
    update: {
      value: days.toString(),
    },
    create: {
      key: 'auto_approval_days',
      value: days.toString(),
      description:
        'Number of days within which return requests are auto-approved',
    },
  });

  revalidatePath('/settings');
}

// Function to check if an email should be blacklisted (called when processing returns)
export async function checkAndBlacklistEmail(email: string): Promise<boolean> {
  // Count return requests from this email
  const returnCount = await database.returnRequest.count({
    where: {
      email,
    },
  });

  // If more than 3 returns, add to blacklist
  if (returnCount > 3) {
    const existing = await database.blacklistEmail.findUnique({
      where: { email },
    });

    if (!existing) {
      await database.blacklistEmail.create({
        data: {
          email,
          reason: 'excessive_returns',
        },
      });
    }
    return true; // Email is blacklisted
  }

  // Check if email is manually blacklisted
  const blacklisted = await database.blacklistEmail.findUnique({
    where: { email },
  });

  return !!blacklisted;
}
