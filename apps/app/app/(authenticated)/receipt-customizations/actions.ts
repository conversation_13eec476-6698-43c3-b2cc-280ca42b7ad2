'use server';

import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

// Get all receipt customizations
export async function getReceiptCustomizations() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const receiptCustomizations = await database.receiptCustomization
    .findMany({
      select: {
        id: true,
        orderName: true,
        email: true,
        recipientName: true,
        note: true,
        downloadCount: true,
        lastDownloaded: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })
    .then((receiptCustomizations) => serializePrisma(receiptCustomizations));

  return receiptCustomizations;
}

// Get a specific receipt customization by ID
export async function getReceiptCustomization(id: string) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const receiptCustomization = await database.receiptCustomization.findUnique({
    where: { id },
  });

  if (!receiptCustomization) {
    notFound();
  }

  return serializePrisma(receiptCustomization);
}
