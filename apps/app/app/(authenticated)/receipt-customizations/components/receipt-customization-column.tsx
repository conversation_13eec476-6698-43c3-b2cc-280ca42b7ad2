'use client';

import { formatDate } from '@repo/design-system/lib/format';
import type { ColumnDef } from '@tanstack/react-table';

// Define the ReceiptCustomization type based on what we're selecting in the action
export type ReceiptCustomizationRow = {
  id: string;
  orderName: string;
  email: string;
  recipientName: string | null;
  note: string | null;
  downloadCount: number;
  lastDownloaded: string | null;
  createdAt: string;
  updatedAt: string;
};

export const columns: ColumnDef<ReceiptCustomizationRow>[] = [
  {
    accessorKey: 'orderName',
    header: 'Order',
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('orderName')}</div>
    ),
  },
  {
    accessorKey: 'email',
    header: 'Customer Email',
  },
  {
    accessorKey: 'recipientName',
    header: 'Recipient Name',
    cell: ({ row }) => {
      const recipientName = row.getValue('recipientName') as string | null;
      return <div>{recipientName || '-'}</div>;
    },
  },
  {
    accessorKey: 'note',
    header: 'Note',
    cell: ({ row }) => {
      const note = row.getValue('note') as string | null;
      return <div>{note || '-'}</div>;
    },
  },
  {
    accessorKey: 'downloadCount',
    header: 'Downloads',
    cell: ({ row }) => {
      const count = row.getValue('downloadCount') as number;
      return <div className="text-center">{count}</div>;
    },
  },
  {
    accessorKey: 'lastDownloaded',
    header: 'Last Downloaded',
    cell: ({ row }) => {
      const date = row.getValue('lastDownloaded') as string | null;
      return <div>{date ? formatDate(new Date(date)) : 'Never'}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created',
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return <div>{formatDate(date)}</div>;
    },
  },
];
