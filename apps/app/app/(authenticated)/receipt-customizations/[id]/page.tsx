import { Header } from '@/app/(authenticated)/components/header';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatDateTime } from '@repo/design-system/lib/format';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { getReceiptCustomization } from '../actions';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const receiptCustomization = await getReceiptCustomization(id);

  const title = 'Senders Return - Receipt Customization';
  const description = `Receipt customization for order ${receiptCustomization.orderName}`;

  return {
    title,
    description,
  };
}

export default async function ReceiptCustomizationDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const receiptCustomization = await getReceiptCustomization(id);

  return (
    <>
      <Header
        pages={['Receipt Customizations']}
        page={receiptCustomization.orderName}
      />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-xl tracking-tight">
            Receipt Customization for Order {receiptCustomization.orderName}
          </h2>
        </div>

        <div className="grid gap-4 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Customization Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Order</span>
                <span>{receiptCustomization.orderName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Customer Email</span>
                <span>{receiptCustomization.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Recipient Name</span>
                <span>{receiptCustomization.recipientName || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Note</span>
                <span>{receiptCustomization.note || '-'}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Download Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Download Count</span>
                <span>{receiptCustomization.downloadCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last Downloaded</span>
                <span>
                  {receiptCustomization.lastDownloaded
                    ? formatDateTime(receiptCustomization.lastDownloaded)
                    : 'Never'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Created</span>
                <span>{formatDateTime(receiptCustomization.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last Updated</span>
                <span>{formatDateTime(receiptCustomization.updatedAt)}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </>
  );
}
