'use client';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import type { Dictionary } from '@repo/internationalization';
import * as z from 'zod';

// Define the form schema
const formSchema = z.object({
  recipientName: z.string().min(1, 'Recipient name is required'),
  note: z.string().optional(),
});

export type ReceiptFormValues = z.infer<typeof formSchema>;

interface ReceiptFormProps {
  defaultValues: {
    recipientName: string;
    note?: string;
  };
  onSubmit: (values: ReceiptFormValues) => void;
  dictionary?: Dictionary | null;
  disabled?: boolean;
}

export default function ReceiptForm({
  defaultValues,
  onSubmit,
  dictionary,
  disabled = false,
}: ReceiptFormProps) {
  // Initialize the form
  const form = useForm<ReceiptFormValues>({
    resolver: zod<PERSON><PERSON><PERSON>ver(formSchema),
    defaultValues,
  });

  // Handle form submission
  function handleSubmit(values: ReceiptFormValues) {
    onSubmit(values);
  }

  // Default text if dictionary is not loaded yet
  const t = dictionary?.receipt?.form || {
    recipient_name: 'Recipient/Company Name',
    recipient_placeholder: 'Casefinite Inc.',
    recipient_description: 'The name that will appear on the receipt',
    note_name: 'Note',
    note_placeholder: 'For the cost of goods',
    note_description: 'Input example: “As payment for goods”.',
    generate: 'Generate Receipt',
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="recipientName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t.recipient_name}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t.recipient_placeholder}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>{t.recipient_description}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="note"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t.note_name}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t.note_placeholder}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>{t.note_description}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit" className="cursor-pointer">
            {t.generate}
          </Button>
        </div>
      </form>
    </Form>
  );
}
