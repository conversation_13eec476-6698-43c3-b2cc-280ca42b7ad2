import { log } from '@repo/observability/log';
import { prisma } from './prisma';

/**
 * Creates a return request in the database
 * @param data The return request data
 * @returns The created return request
 */
export async function createReturnRequest(data: {
  orderName: string;
  email: string;
  returnNumber: string;
  returnReason: string;
  exchangeType?: string | null;
  defectType?: string | null;
  defectDetails?: string | null;
  returnLabelOption?: string | null;
  refundFee?: number | null;
  processed: boolean;
  status: string;
  returnItems: {
    create: {
      lineItemId: string;
      title: string;
      variantTitle?: string | null;
      quantity: number;
      price: number;
      currency: string;
      sku?: string | null;
      returnReason?: string | null;
      exchangeVariant?: string | null;
    }[];
  };
  defectPhotos: {
    create: {
      url: string;
    }[];
  };
}) {
  try {
    log.info('Creating return request in database', {
      orderName: data.orderName,
    });

    const returnRequest = await prisma.returnRequest.create({
      data: {
        orderName: data.orderName,
        email: data.email,
        returnNumber: data.returnNumber,
        returnReason: data.returnReason,
        exchangeType: data.exchangeType,
        defectType: data.defectType,
        defectDetails: data.defectDetails,
        returnLabelOption: data.returnLabelOption,
        refundFee: data.refundFee,
        processed: data.processed,
        status: data.status,
        returnItems: data.returnItems,
        defectPhotos: data.defectPhotos,
      },
      include: {
        returnItems: true,
        defectPhotos: true,
      },
    });

    log.info('Return request created successfully', {
      returnNumber: returnRequest.returnNumber,
    });
    return returnRequest;
  } catch (error) {
    log.error('Error creating return request', {
      error,
      orderName: data.orderName,
    });
    throw error;
  }
}

/**
 * Creates an address change request in the database
 * @param data The address change request data
 * @returns The created address change request
 */
export async function createAddressChangeRequest(data: {
  orderName: string;
  email: string;
  originalAddress: Record<string, any>;
  newAddress: Record<string, any>;
  status: string;
}) {
  try {
    log.info('Creating address change request in database', {
      orderName: data.orderName,
    });

    const addressChangeRequest = await prisma.addressChangeRequest.create({
      data: {
        orderName: data.orderName,
        email: data.email,
        originalAddress: data.originalAddress,
        newAddress: data.newAddress,
        status: data.status,
      },
    });

    log.info('Address change request created successfully', {
      id: addressChangeRequest.id,
    });
    return addressChangeRequest;
  } catch (error) {
    log.error('Error creating address change request', {
      error,
      orderName: data.orderName,
    });
    throw error;
  }
}

/**
 * Creates a receipt customization in the database
 * @param data The receipt customization data
 * @returns The created receipt customization
 */
export async function createReceiptCustomization(data: {
  orderName: string;
  email: string;
  recipientName?: string | null;
  note?: string | null;
}) {
  try {
    log.info('Creating receipt customization in database', {
      orderName: data.orderName,
    });

    // Check if a receipt customization already exists for this order and email
    const existingCustomization = await prisma.receiptCustomization.findUnique({
      where: {
        orderName_email: {
          orderName: data.orderName,
          email: data.email,
        },
      },
    });

    if (existingCustomization) {
      // Check if already customized and prevent further edits
      if (existingCustomization.isCustomized) {
        throw new Error(
          'Receipt has already been customized and cannot be edited again.'
        );
      }

      // Update the existing customization and mark as customized
      const updatedCustomization = await prisma.receiptCustomization.update({
        where: {
          id: existingCustomization.id,
        },
        data: {
          recipientName: data.recipientName,
          note: data.note,
          isCustomized: true,
        },
      });

      log.info('Receipt customization updated successfully', {
        id: updatedCustomization.id,
      });
      return updatedCustomization;
    }

    // Create a new customization and mark as customized
    const receiptCustomization = await prisma.receiptCustomization.create({
      data: {
        orderName: data.orderName,
        email: data.email,
        recipientName: data.recipientName,
        note: data.note,
        isCustomized: true,
        downloadCount: 0,
      },
    });

    log.info('Receipt customization created successfully', {
      id: receiptCustomization.id,
    });
    return receiptCustomization;
  } catch (error) {
    log.error('Error creating receipt customization', {
      error,
      orderName: data.orderName,
    });
    throw error;
  }
}

/**
 * Updates the download count for a receipt customization
 * @param id The receipt customization ID
 * @returns The updated receipt customization
 */
export async function updateReceiptDownloadCount(id: string) {
  try {
    log.info('Updating receipt download count', { id });

    const updatedCustomization = await prisma.receiptCustomization.update({
      where: {
        id,
      },
      data: {
        downloadCount: {
          increment: 1,
        },
        lastDownloaded: new Date(),
      },
    });

    log.info('Receipt download count updated successfully', {
      id,
      newCount: updatedCustomization.downloadCount,
    });

    return updatedCustomization;
  } catch (error) {
    log.error('Error updating receipt download count', { error, id });
    throw error;
  }
}

/**
 * Creates an admin notification in the database
 * @param data The notification data
 * @returns The created notification
 */
export async function createAdminNotification(data: {
  type: string;
  message: string;
  relatedOrderName?: string;
}) {
  try {
    log.info('Creating admin notification', { type: data.type });

    const notification = await prisma.adminNotification.create({
      data: {
        type: data.type,
        message: data.message,
        relatedOrderName: data.relatedOrderName,
        read: false,
      },
    });

    log.info('Admin notification created successfully', {
      id: notification.id,
    });
    return notification;
  } catch (error) {
    log.error('Error creating admin notification', { error, type: data.type });
    throw error;
  }
}
